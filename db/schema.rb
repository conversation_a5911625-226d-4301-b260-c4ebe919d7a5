# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_08_04_122301) do
  create_table "active_admin_comments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "namespace"
    t.text "body"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "author_type"
    t.bigint "author_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["author_type", "author_id"], name: "index_active_admin_comments_on_author_type_and_author_id"
    t.index ["namespace"], name: "index_active_admin_comments_on_namespace"
    t.index ["resource_type", "resource_id"], name: "index_active_admin_comments_on_resource_type_and_resource_id"
  end

  create_table "active_admin_managed_resources", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "class_name", null: false
    t.string "action", null: false
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["class_name", "action", "name"], name: "active_admin_managed_resources_index", unique: true
  end

  create_table "active_admin_permissions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "managed_resource_id", null: false
    t.integer "role", limit: 1, default: 0, null: false
    t.integer "state", limit: 1, default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["managed_resource_id", "role"], name: "active_admin_permissions_index", unique: true
  end

  create_table "active_storage_attachments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", precision: nil, null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "admin_media", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "url"
    t.string "media_type"
    t.bigint "admin_user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_admin_media_on_admin_user_id"
  end

  create_table "admin_users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "name", default: "", null: false, collation: "utf8mb4_unicode_ci"
    t.bigint "phone"
    t.bigint "admin_medium_id"
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "role", default: "normal", null: false
    t.string "provider"
    t.string "uid"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "floww_user_id"
    t.boolean "active", default: true
    t.index ["admin_medium_id"], name: "index_admin_users_on_admin_medium_id"
    t.index ["email"], name: "index_admin_users_on_email", unique: true
    t.index ["provider", "uid"], name: "index_admin_users_on_provider_and_uid", unique: true
    t.index ["reset_password_token"], name: "index_admin_users_on_reset_password_token", unique: true
  end

  create_table "agent_partners", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "phone", null: false
    t.string "name"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["phone"], name: "index_agent_partners_on_phone", unique: true
  end

  create_table "badge_icon_groups", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "circle_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name", null: false
    t.index ["circle_id"], name: "index_badge_icon_groups_on_circle_id", unique: true
  end

  create_table "badge_icons", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "color", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "badge_icon_group_id", null: false
    t.bigint "admin_medium_id"
    t.index ["admin_medium_id"], name: "index_badge_icons_on_admin_medium_id"
    t.index ["badge_icon_group_id", "color"], name: "index_badge_icons_on_badge_icon_group_id_and_color", unique: true
    t.index ["badge_icon_group_id"], name: "index_badge_icons_on_badge_icon_group_id"
  end

  create_table "badge_roles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "text"
    t.boolean "prefix"
    t.boolean "active"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "role_type"
  end

  create_table "blocked_users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "blocked_user_id"
    t.string "reason"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["blocked_user_id"], name: "index_blocked_users_on_blocked_user_id"
    t.index ["user_id", "blocked_user_id"], name: "index_blocked_users_on_user_id_and_blocked_user_id", unique: true
    t.index ["user_id"], name: "index_blocked_users_on_user_id"
  end

  create_table "campaigns", charset: "utf8mb4", collation: "utf8mb4_unicode_ci", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "start_time", null: false
    t.datetime "end_time", null: false
    t.boolean "active", default: true
    t.json "cohort_details_json", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "admin_medium_id", null: false
    t.index ["admin_medium_id"], name: "index_campaigns_on_admin_medium_id"
  end

  create_table "circle_frames", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "circle_id", null: false
    t.bigint "frame_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id"], name: "index_circle_frames_on_circle_id"
    t.index ["frame_id"], name: "index_circle_frames_on_frame_id"
  end

  create_table "circle_monthly_usages", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "circle_id", null: false
    t.integer "fan_posters_limit"
    t.integer "fan_posters_usage", default: 0
    t.integer "channel_message_limit"
    t.integer "channel_message_usage", default: 0
    t.integer "channel_notification_limit"
    t.integer "channel_notification_usage", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "month_start"
    t.date "month_end"
    t.boolean "active", default: true
    t.index ["circle_id", "month_start", "month_end", "active"], name: "index_on_circle_monthly_usages_on_circle_id_and_active_tenure"
    t.index ["circle_id"], name: "index_circle_monthly_usages_on_circle_id"
  end

  create_table "circle_package_mappings", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "circle_id", null: false
    t.bigint "circle_package_id", null: false
    t.date "start_date", null: false
    t.date "end_date", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id", "start_date", "end_date", "active"], name: "index_on_circle_tenure_active"
    t.index ["circle_id"], name: "index_circle_package_mappings_on_circle_id"
    t.index ["circle_package_id"], name: "index_circle_package_mappings_on_circle_package_id"
    t.index ["start_date", "end_date", "active"], name: "index_on_tenure_active"
  end

  create_table "circle_packages", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.boolean "enable_channel", default: false
    t.boolean "enable_fan_posters", default: false
    t.boolean "eligibile_for_post_push_notifications", default: false
    t.boolean "eligibile_for_wati", default: false
    t.integer "fan_poster_creatives_limit"
    t.integer "channel_post_msg_limit"
    t.integer "channel_post_msg_notification_limit"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "circle_permission_groups", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "circle_id"
    t.integer "circle_type"
    t.integer "circle_level"
    t.bigint "permission_group_id", null: false
    t.boolean "is_user_joined", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id", "is_user_joined"], name: "index_circle_permission_groups_on_circle_id_and_is_user_joined", unique: true
    t.index ["circle_id"], name: "index_circle_permission_groups_on_circle_id"
    t.index ["circle_type", "circle_level", "is_user_joined"], name: "index_on_circle_type_level_and_is_user_joined", unique: true
    t.index ["permission_group_id"], name: "index_circle_permission_groups_on_permission_group_id"
  end

  create_table "circle_photos", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "photo_id"
    t.bigint "circle_id"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "photo_order", limit: 1
    t.string "photo_type", null: false
    t.index ["circle_id", "photo_order", "photo_type"], name: "index_circle_photos_on_circle_id_and_photo_order_and_photo_type", unique: true
    t.index ["circle_id", "photo_type"], name: "index_circle_photos_on_circle_id_and_photo_type"
    t.index ["circle_id"], name: "index_circle_photos_on_circle_id"
    t.index ["photo_id"], name: "index_circle_photos_on_photo_id"
  end

  create_table "circle_premium_interests", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "circle_id", null: false
    t.string "key", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id"], name: "index_circle_premium_interests_on_circle_id"
    t.index ["user_id", "circle_id", "key"], name: "index_circle_premium_interests_on_user_id_and_circle_id_and_key", unique: true
    t.index ["user_id"], name: "index_circle_premium_interests_on_user_id"
  end

  create_table "circle_story_threads", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "circle_id"
    t.string "name"
    t.boolean "active", default: true
    t.timestamp "expires_at"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["circle_id"], name: "index_circle_story_threads_on_circle_id"
  end

  create_table "circles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", limit: 191, collation: "utf8mb4_unicode_ci"
    t.string "name_en"
    t.text "dynamic_link"
    t.integer "level"
    t.integer "parent_circle_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "photo_id"
    t.text "description"
    t.bigint "head_user_id"
    t.integer "circle_type", default: 0
    t.string "short_info"
    t.bigint "members_count", default: 0
    t.string "short_name"
    t.bigint "banner_id"
    t.string "website_url"
    t.string "conversation_type", default: "none", null: false
    t.string "slogan", default: ""
    t.string "slogan_icon_type"
    t.bigint "slogan_icon_id"
    t.bigint "relationship_manager_id"
    t.boolean "official", default: false
    t.index ["banner_id"], name: "index_circles_on_banner_id"
    t.index ["circle_type"], name: "index_circles_on_circle_type"
    t.index ["head_user_id"], name: "index_circles_on_head_user_id"
    t.index ["id", "level", "circle_type"], name: "index_circles_on_id_and_level_and_circle_type"
    t.index ["level"], name: "index_circles_on_level"
    t.index ["parent_circle_id"], name: "index_circles_on_parent_circle_id"
    t.index ["photo_id"], name: "index_circles_on_photo_id"
    t.index ["relationship_manager_id"], name: "index_circles_on_relationship_manager_id"
    t.index ["slogan_icon_type", "slogan_icon_id"], name: "index_circles_on_slogan_icon"
  end

  create_table "circles_relations", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "first_circle_id", null: false
    t.integer "second_circle_id", null: false
    t.string "relation"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active", "relation"], name: "index_circles_relations_on_active_and_relation"
    t.index ["first_circle_id", "second_circle_id"], name: "index_circles_relations_on_first_circle_id_and_second_circle_id", unique: true
  end

  create_table "contacts", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "phone"
    t.boolean "sent", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "network_density"
    t.index ["phone"], name: "index_contacts_on_phone", unique: true
    t.index ["sent"], name: "index_contacts_on_sent"
  end

  create_table "devices", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "model"
    t.string "make"
    t.integer "price"
    t.date "launch_date"
    t.json "raw_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["model"], name: "index_devices_on_model", unique: true
  end

  create_table "entity_names", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "entity_type", null: false
    t.bigint "entity_id", null: false
    t.text "name_en"
    t.text "name_te"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["entity_type", "entity_id"], name: "index_entity_names_on_entity"
  end

  create_table "event_circles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "event_id", null: false
    t.bigint "circle_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id"], name: "index_event_circles_on_circle_id"
    t.index ["event_id"], name: "index_event_circles_on_event_id"
  end

  create_table "events", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.integer "priority", limit: 1
    t.string "creator_type", null: false
    t.bigint "creator_id", null: false
    t.datetime "start_time", precision: nil
    t.datetime "end_time", precision: nil
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_type", "creator_id"], name: "index_events_on_creator"
  end

  create_table "excluded_user_circle_permissions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "circle_id"
    t.string "permission_identifier", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id"], name: "index_excluded_user_circle_permissions_on_circle_id"
    t.index ["user_id", "circle_id", "permission_identifier"], name: "index_excluded_user_circle_permissions_with_all_cols", unique: true
    t.index ["user_id"], name: "index_excluded_user_circle_permissions_on_user_id"
  end

  create_table "excluded_user_circles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "circle_id"
    t.bigint "excluded_by_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "conversation_type", default: "none"
    t.index ["circle_id"], name: "index_excluded_user_circles_on_circle_id"
    t.index ["user_id", "circle_id", "conversation_type"], name: "user_circle_conversation_type_index", unique: true
    t.index ["user_id"], name: "index_excluded_user_circles_on_user_id"
  end

  create_table "feed_items", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "feed_type"
    t.integer "feed_type_id"
    t.integer "item_type"
    t.integer "item_id"
    t.integer "score"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "fonts", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name_font", null: false
    t.string "badge_font", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name_font", "badge_font"], name: "index_fonts_on_name_font_and_badge_font", unique: true
  end

  create_table "frame_recommendations", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "frame_id", null: false
    t.string "user_name_type", null: false
    t.string "badge_text_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["frame_id", "user_name_type", "badge_text_type"], name: "unique_index_on_frames_recommendations", unique: true
    t.index ["frame_id"], name: "index_frame_recommendations_on_frame_id"
  end

  create_table "frame_views", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "frame_id", null: false
    t.bigint "user_id", null: false
    t.datetime "viewed_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["frame_id"], name: "index_frame_views_on_frame_id"
    t.index ["user_id", "created_at"], name: "index_frame_views_on_user_id_and_created_at"
    t.index ["user_id"], name: "index_frame_views_on_user_id"
  end

  create_table "frames", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "identifier"
    t.string "frame_type", null: false
    t.boolean "gold_border", default: false
    t.boolean "has_shadow_color", default: false
    t.boolean "is_neutral_frame", default: false
    t.boolean "has_footer_party_icon", default: false
    t.string "identity_type"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "badge_strip", default: true
    t.boolean "user_position_back"
    t.boolean "outer_frame", default: true
    t.bigint "font_id", null: false
    t.index ["font_id"], name: "index_frames_on_font_id"
    t.index ["frame_type", "gold_border", "has_shadow_color", "is_neutral_frame", "has_footer_party_icon", "identity_type", "active", "badge_strip", "user_position_back", "outer_frame", "font_id"], name: "unique_index_on_frames", unique: true
    t.index ["frame_type"], name: "index_frames_on_frame_type"
  end

  create_table "garuda_notifications", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "notification_id"
    t.string "title"
    t.string "body"
    t.string "path"
    t.string "status", default: "created"
    t.integer "sent_count"
    t.datetime "send_at"
    t.string "sidekiq_job_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "sent_at"
    t.integer "admin_user_id"
    t.bigint "location_circle_id"
    t.bigint "party_circle_id"
    t.boolean "exclusive_party_users", default: false
    t.index ["location_circle_id"], name: "index_garuda_notifications_on_location_circle_id"
    t.index ["party_circle_id"], name: "index_garuda_notifications_on_party_circle_id"
  end

  create_table "hashtag_circles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "hashtag_id", null: false
    t.bigint "circle_id", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id"], name: "index_hashtag_circles_on_circle_id"
    t.index ["hashtag_id"], name: "index_hashtag_circles_on_hashtag_id"
  end

  create_table "hashtag_likes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "hashtag_id", null: false
    t.bigint "user_id", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["hashtag_id"], name: "index_hashtag_likes_on_hashtag_id"
    t.index ["user_id"], name: "index_hashtag_likes_on_user_id"
  end

  create_table "hashtags", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "name", collation: "utf8mb4_bin"
    t.text "identifier", collation: "utf8mb4_bin"
    t.text "dynamic_link"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "likes_count", default: 0
    t.integer "opinions_count", default: 0
    t.index "(cast(lower(`name`) as char(255) charset utf8mb4) collate utf8mb4_bin)", name: "function_index_name"
    t.index ["active", "identifier"], name: "index_hashtags_on_active_and_identifier", length: { identifier: 515 }
    t.index ["created_at"], name: "index_hashtags_on_created_at"
  end

  create_table "internal_permissions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.string "identifier"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "internal_role_permissions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "internal_role_id"
    t.bigint "internal_permission_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["internal_permission_id"], name: "index_internal_role_permissions_on_internal_permission_id"
    t.index ["internal_role_id"], name: "index_internal_role_permissions_on_internal_role_id"
  end

  create_table "internal_roles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.string "identifier"
    t.bigint "circle_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["circle_id"], name: "index_internal_roles_on_circle_id"
  end

  create_table "internal_user_roles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "internal_role_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["internal_role_id"], name: "index_internal_user_roles_on_internal_role_id"
    t.index ["user_id"], name: "index_internal_user_roles_on_user_id"
  end

  create_table "invalid_user_invites", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "phone"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_invalid_user_invites_on_user_id"
  end

  create_table "invoices", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "transaction_reference_id", null: false
    t.string "type", null: false
    t.integer "total_amount", null: false
    t.integer "credits", default: 0
    t.integer "amount", null: false
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_invoices_on_user_id"
  end

  create_table "item_prices", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.decimal "price", precision: 10, default: "0", null: false
    t.decimal "maintenance_price", precision: 10, default: "0"
    t.integer "duration_in_months", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_type", "item_id", "duration_in_months", "active"], name: "index_item_prices_on_item_and_duration_and_active", unique: true
    t.index ["item_type", "item_id"], name: "index_item_prices_on_item"
  end

  create_table "lead_type_sops", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "lead_type"
    t.text "sop"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lead_type"], name: "index_lead_type_sops_on_lead_type", unique: true
  end

  create_table "leader_projects", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "user_role_id", null: false
    t.string "title", null: false, collation: "utf8mb4_unicode_ci"
    t.string "body", null: false, collation: "utf8mb4_unicode_ci"
    t.date "project_date", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "creator_type"
    t.bigint "creator_id"
    t.index ["creator_type", "creator_id"], name: "index_leader_projects_on_creator"
    t.index ["user_id"], name: "index_leader_projects_on_user_id"
    t.index ["user_role_id"], name: "index_leader_projects_on_user_role_id"
  end

  create_table "links", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "url"
    t.string "title", limit: 191, collation: "utf8mb4_unicode_ci"
    t.text "description", collation: "utf8mb4_unicode_ci"
    t.string "image"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "user_id"
    t.index ["url", "active"], name: "index_links_on_url_and_active", unique: true
  end

  create_table "metadata", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "key"
    t.text "value"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "entity_type"
    t.bigint "entity_id"
    t.index ["entity_type", "entity_id", "key"], name: "index_on_entity_type_entity_id_and_key"
    t.index ["entity_type", "entity_id"], name: "index_metadata_on_entity"
    t.index ["key", "created_at"], name: "index_on_key_and_created_at"
    t.index ["key"], name: "index_metadata_on_key"
  end

  create_table "nicknames", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.boolean "active"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["name"], name: "index_nicknames_on_name", unique: true
  end

  create_table "notifications", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "description", collation: "utf8mb4_unicode_ci"
    t.integer "notification_type"
    t.bigint "user_id"
    t.boolean "delivered", default: false
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "deep_link"
    t.boolean "read", default: false
    t.string "entity_type"
    t.integer "entity_id"
    t.boolean "received", default: false
    t.index ["user_id", "active", "read"], name: "index_notifications_on_user_id_active_and_read"
    t.index ["user_id", "active", "received"], name: "index_notifications_on_user_id_active_and_received"
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "one_time_passwords", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "password_hash"
    t.bigint "user_id"
    t.datetime "expires_at", precision: nil
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id"], name: "index_one_time_passwords_on_user_id"
  end

  create_table "order_items", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "order_id", null: false
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.bigint "item_price_id"
    t.bigint "parent_order_item_id"
    t.integer "duration_in_months", null: false
    t.decimal "total_item_price", precision: 10, default: "0", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_order_items_on_active"
    t.index ["item_price_id"], name: "index_order_items_on_item_price_id"
    t.index ["item_type", "item_id"], name: "index_order_items_on_item"
    t.index ["order_id", "item_type", "item_id", "active"], name: "unique_key_for_order_items_index", unique: true
    t.index ["order_id"], name: "index_order_items_on_order_id"
    t.index ["parent_order_item_id"], name: "index_order_items_on_parent_order_item_id"
  end

  create_table "order_transactions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "order_id", null: false
    t.string "status"
    t.decimal "amount", precision: 10
    t.string "gateway"
    t.string "gateway_transaction_id"
    t.text "raw_pg_request"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "checkout_url"
    t.string "transaction_id"
    t.index ["order_id"], name: "index_order_transactions_on_order_id"
    t.index ["transaction_id"], name: "index_order_transactions_on_transaction_id", unique: true
  end

  create_table "orders", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "total_amount", precision: 10, default: "0", null: false
    t.decimal "payable_amount", precision: 10, default: "0", null: false
    t.decimal "discount_amount", precision: 10, default: "0"
    t.bigint "referred_by"
    t.boolean "old_version_default", default: true
    t.bigint "plan_id"
    t.index ["plan_id"], name: "index_orders_on_plan_id"
    t.index ["user_id"], name: "index_orders_on_user_id"
  end

  create_table "permission_group_permissions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "permission_group_id", null: false
    t.integer "permission_identifier", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["permission_group_id", "permission_identifier"], name: "index_on_permission_groups_id_and_permission_identifier", unique: true
    t.index ["permission_group_id"], name: "index_permission_group_permissions_on_permission_group_id"
  end

  create_table "permission_groups", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_permission_groups_on_name", unique: true
  end

  create_table "photo_labels", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "photo_id"
    t.integer "label_type"
    t.string "category"
    t.string "parent_category"
    t.decimal "confidence", precision: 5, scale: 2
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["photo_id", "category"], name: "index_photo_labels_on_photo_id_and_category", unique: true
    t.index ["photo_id"], name: "index_photo_labels_on_photo_id"
  end

  create_table "photos", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "url"
    t.integer "width", default: 0
    t.integer "height", default: 0
    t.string "dominant_dark_color"
    t.bigint "user_id"
    t.boolean "active", default: true
    t.boolean "explicit", default: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.text "base64"
    t.string "path"
    t.string "service", null: false
    t.string "bucket"
    t.index ["user_id"], name: "index_photos_on_user_id"
  end

  create_table "plan_products", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "plan_id", null: false
    t.bigint "product_id", null: false
    t.integer "quantity", limit: 2, default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["plan_id", "product_id"], name: "index_plan_products_on_plan_id_and_product_id", unique: true
    t.index ["plan_id"], name: "index_plan_products_on_plan_id"
    t.index ["product_id"], name: "index_plan_products_on_product_id"
  end

  create_table "plans", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.integer "duration_in_months"
    t.integer "total_amount"
    t.integer "discount_amount"
    t.integer "amount"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["duration_in_months"], name: "index_plans_on_duration_in_months"
  end

  create_table "poll_options", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "description"
    t.bigint "poll_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["poll_id"], name: "index_poll_options_on_poll_id"
  end

  create_table "polls", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "post_id"
    t.datetime "ends_at", precision: nil
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["post_id"], name: "index_polls_on_post_id"
  end

  create_table "post_bounces", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "post_id", null: false
    t.bigint "circle_id", null: false
    t.integer "user_id", null: false
    t.integer "score"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id"], name: "index_post_bounces_on_circle_id"
    t.index ["post_id", "user_id", "circle_id"], name: "index_post_bounces_on_post_id_and_user_id_and_circle_id", unique: true
    t.index ["post_id"], name: "index_post_bounces_on_post_id"
  end

  create_table "post_circles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "post_id"
    t.bigint "circle_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["circle_id"], name: "index_post_circles_on_circle_id"
    t.index ["post_id"], name: "index_post_circles_on_post_id"
  end

  create_table "post_comments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "comment", collation: "utf8mb4_unicode_ci"
    t.bigint "post_id"
    t.bigint "user_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["post_id"], name: "index_post_comments_on_post_id"
    t.index ["user_id"], name: "index_post_comments_on_user_id"
  end

  create_table "post_hashtags", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "tag", collation: "utf8mb4_bin"
    t.bigint "post_id", null: false
    t.bigint "hashtag_id", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active", "hashtag_id"], name: "index_post_hashtags_on_active_and_hashtag_id"
    t.index ["hashtag_id"], name: "index_post_hashtags_on_hashtag_id"
    t.index ["post_id"], name: "index_post_hashtags_on_post_id"
  end

  create_table "post_likes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "post_id"
    t.bigint "user_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["active"], name: "index_post_likes_on_active"
    t.index ["created_at"], name: "index_post_likes_on_created_at"
    t.index ["post_id", "user_id"], name: "index_post_likes_on_post_id_and_user_id", unique: true
    t.index ["post_id"], name: "index_post_likes_on_post_id"
    t.index ["user_id"], name: "index_post_likes_on_user_id"
  end

  create_table "post_photos", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "photo_id"
    t.bigint "post_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["photo_id"], name: "index_post_photos_on_photo_id"
    t.index ["post_id", "photo_id"], name: "index_post_photos_on_post_id_and_photo_id", unique: true
    t.index ["post_id"], name: "index_post_photos_on_post_id"
  end

  create_table "post_reports", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "post_id", null: false
    t.bigint "user_id", null: false
    t.string "report_reason"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["post_id"], name: "index_post_reports_on_post_id"
    t.index ["user_id"], name: "index_post_reports_on_user_id"
  end

  create_table "post_user_tags", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "post_id", null: false
    t.bigint "user_id", null: false
    t.string "tag"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["post_id"], name: "index_post_user_tags_on_post_id"
    t.index ["user_id"], name: "index_post_user_tags_on_user_id"
  end

  create_table "post_videos", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "post_id"
    t.bigint "video_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["post_id"], name: "index_post_videos_on_post_id"
    t.index ["video_id"], name: "index_post_videos_on_video_id"
  end

  create_table "post_views", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "post_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_post_views_on_created_at"
    t.index ["post_id"], name: "index_post_views_on_post_id"
    t.index ["user_id"], name: "index_post_views_on_user_id"
  end

  create_table "poster_creative_circles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "poster_creative_id", null: false
    t.bigint "circle_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id"], name: "index_poster_creative_circles_on_circle_id"
    t.index ["poster_creative_id"], name: "index_poster_creative_circles_on_poster_creative_id"
  end

  create_table "poster_creative_views", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "poster_creative_id", null: false
    t.bigint "user_id", null: false
    t.datetime "viewed_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["poster_creative_id"], name: "index_poster_creative_views_on_poster_creative_id"
    t.index ["user_id", "id"], name: "index_poster_creative_views_on_user_id_and_id_desc", order: { id: :desc }
    t.index ["user_id"], name: "index_poster_creative_views_on_user_id"
  end

  create_table "poster_creatives", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "event_id"
    t.integer "creative_kind", limit: 1, null: false
    t.string "creator_type", null: false
    t.bigint "creator_id", null: false
    t.datetime "start_time", precision: nil
    t.datetime "end_time", precision: nil
    t.boolean "paid", default: false
    t.string "photo_v2_type"
    t.bigint "photo_v2_id"
    t.integer "h1_leader_photo_ring_type", limit: 1, null: false
    t.integer "h2_leader_photo_ring_type", limit: 1, null: false
    t.boolean "primary", default: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "photo_v1_type"
    t.bigint "photo_v1_id"
    t.string "photo_v3_type"
    t.bigint "photo_v3_id"
    t.integer "designer_id"
    t.string "pose_preference_order", default: "", null: false
    t.index ["creator_type", "creator_id"], name: "index_poster_creatives_on_creator"
    t.index ["event_id"], name: "index_poster_creatives_on_event_id"
    t.index ["photo_v1_type", "photo_v1_id"], name: "index_poster_creatives_on_photo_v1"
    t.index ["photo_v2_type", "photo_v2_id"], name: "index_poster_creatives_on_photo_v2"
    t.index ["photo_v3_type", "photo_v3_id"], name: "index_poster_creatives_on_photo_v3"
  end

  create_table "poster_layout_remarks", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_poster_layout_id", null: false
    t.bigint "admin_user_id", null: false
    t.text "remarks", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_poster_layout_remarks_on_admin_user_id"
    t.index ["user_poster_layout_id"], name: "index_poster_layout_remarks_on_user_poster_layout_id"
  end

  create_table "poster_photos", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "poster_id"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "new_version_photo_url"
    t.integer "leader_photo_ring_color", limit: 1
    t.text "old_version_photo_url"
    t.integer "photo_orientation", limit: 1
    t.string "photo_type"
    t.bigint "photo_id"
    t.index ["photo_type", "photo_id"], name: "index_poster_photos_on_photo"
    t.index ["poster_id"], name: "index_poster_photos_on_poster_id"
  end

  create_table "poster_shares", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "poster_creative_id", null: false
    t.bigint "frame_id"
    t.bigint "user_id", null: false
    t.datetime "actioned_at", null: false
    t.string "method"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["frame_id"], name: "index_poster_shares_on_frame_id"
    t.index ["poster_creative_id"], name: "index_poster_shares_on_poster_creative_id"
    t.index ["user_id", "created_at"], name: "index_poster_shares_on_user_id_and_created_at"
    t.index ["user_id", "frame_id", "created_at"], name: "index_poster_shares_on_user_id_and_frame_id_and_created_at"
    t.index ["user_id"], name: "index_poster_shares_on_user_id"
  end

  create_table "posters", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "start_time", precision: nil
    t.datetime "end_time", precision: nil
    t.boolean "active", default: true
    t.bigint "circle_id"
    t.bigint "admin_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "poster_type", limit: 1
    t.index ["admin_user_id"], name: "index_posters_on_admin_user_id"
  end

  create_table "posts", charset: "utf8mb4", collation: "utf8mb4_bin", force: :cascade do |t|
    t.text "content"
    t.text "dynamic_link"
    t.bigint "user_id"
    t.integer "parent_post_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "link_id"
    t.boolean "is_poll", default: false
    t.string "app_version"
    t.integer "comments_type", limit: 2, default: 0
    t.bigint "party_id_on_comments_type"
    t.boolean "has_tagged_circles"
    t.decimal "news_worthy_score", precision: 5, scale: 2
    t.index ["active", "parent_post_id"], name: "index_posts_on_active_and_parent_post_id"
    t.index ["active"], name: "index_posts_on_active"
    t.index ["created_at"], name: "index_posts_on_created_at"
    t.index ["user_id", "active"], name: "index_posts_on_user_id_and_active"
    t.index ["user_id"], name: "index_posts_on_user_id"
  end

  create_table "premium_pitches", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "status", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "lead_type"
    t.string "source"
    t.string "crm_stage"
    t.index ["user_id"], name: "index_premium_pitches_on_user_id", unique: true
  end

  create_table "products", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "active", default: true
  end

  create_table "professions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "name_en"
    t.bigint "admin_medium_id", null: false
    t.integer "ordinal", limit: 2, default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_medium_id"], name: "index_professions_on_admin_medium_id"
  end

  create_table "profile_views", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "viewer_id", null: false
    t.datetime "viewed_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "viewed_at"], name: "index_profile_views_on_user_id_and_viewed_at"
    t.index ["user_id", "viewer_id"], name: "index_profile_views_on_user_id_and_viewer_id", unique: true
    t.index ["user_id"], name: "index_profile_views_on_user_id"
    t.index ["viewer_id"], name: "index_profile_views_on_viewer_id"
  end

  create_table "red_words", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "word", null: false
    t.boolean "is_hate_speech", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "admin_user_id"
    t.index ["word"], name: "index_red_words_on_word", unique: true
  end

  create_table "reports", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.integer "reference_id"
    t.string "reference_type"
    t.string "report_reason"
    t.bigint "user_id"
    t.boolean "active"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["reference_id", "reference_type"], name: "index_reports_reference"
    t.index ["user_id"], name: "index_reports_user_id"
  end

  create_table "roles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.integer "quota_type", null: false
    t.bigint "quota_value"
    t.boolean "has_badge"
    t.integer "badge_icon"
    t.boolean "badge_ring"
    t.string "badge_color"
    t.integer "grade_level", null: false
    t.integer "primary_circle_type"
    t.integer "primary_circle_level"
    t.boolean "has_secondary_circle"
    t.integer "secondary_circle_type"
    t.integer "secondary_circle_level"
    t.boolean "primary_circle_to_badge_text", default: true
    t.boolean "secondary_circle_to_badge_text", default: false
    t.boolean "prefix", default: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "badge_icon_group_id"
    t.boolean "badge_icon_ribbon"
    t.boolean "show_badge_banner", default: true
    t.bigint "parent_circle_id"
    t.integer "parent_circle_level"
    t.boolean "has_purview", default: false
    t.integer "purview_level"
    t.bigint "permission_group_id", null: false
    t.boolean "has_badge_icon", default: false
    t.boolean "parent_circle_to_badge_text"
    t.boolean "purview_circle_to_badge_text"
    t.string "display_name_order", default: "", null: false
    t.boolean "has_free_text", default: false
    t.string "name_en", null: false
    t.index ["badge_icon_group_id"], name: "index_roles_on_badge_icon_group_id"
    t.index ["name", "parent_circle_id", "parent_circle_level", "purview_level"], name: "unique_role", unique: true
  end

  create_table "singular_data", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.integer "invited_by"
    t.string "utm_medium"
    t.string "utm_source"
    t.string "utm_campaign"
    t.string "invited_via"
    t.string "creative_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "installed_on", precision: nil
    t.index ["invited_by"], name: "index_singular_data_on_invited_by"
    t.index ["user_id"], name: "index_singular_data_on_user_id"
    t.index ["user_id"], name: "unique_index_singular_data_on_user_id", unique: true
  end

  create_table "sub_circle_filters", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "sub_circle_id"
    t.string "filter_key"
    t.string "filter_value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["sub_circle_id", "filter_key"], name: "index_sub_circle_filters_on_sub_circle_id_and_filter_key"
    t.index ["sub_circle_id"], name: "index_sub_circle_filters_on_sub_circle_id"
  end

  create_table "sub_professions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "name_en"
    t.bigint "profession_id", null: false
    t.integer "ordinal", limit: 2, default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["profession_id"], name: "index_sub_professions_on_profession_id"
  end

  create_table "subscription_charge_refunds", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "subscription_charge_id", null: false
    t.bigint "user_id", null: false
    t.string "pg_id"
    t.json "pg_json"
    t.integer "amount", null: false
    t.string "status", null: false
    t.string "reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["subscription_charge_id"], name: "index_subscription_charge_refunds_on_subscription_charge_id"
    t.index ["user_id"], name: "index_subscription_charge_refunds_on_user_id"
  end

  create_table "subscription_charges", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "subscription_id", null: false
    t.bigint "user_id", null: false
    t.integer "amount", null: false
    t.datetime "charge_date", null: false
    t.string "status", null: false
    t.string "pg_id", null: false
    t.string "pg_reference_id"
    t.json "pg_json"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "payment_id"
    t.integer "attempt_number"
    t.datetime "success_at"
    t.integer "charge_amount", default: 0, null: false
    t.integer "refunded_amount", default: 0, null: false
    t.index ["payment_id", "attempt_number"], name: "index_subscription_charges_on_payment_id_and_attempt_number", unique: true
    t.index ["pg_id"], name: "index_subscription_charges_on_pg_id"
    t.index ["pg_reference_id"], name: "index_subscription_charges_on_pg_reference_id"
    t.index ["subscription_id"], name: "index_subscription_charges_on_subscription_id"
    t.index ["user_id"], name: "index_subscription_charges_on_user_id"
  end

  create_table "subscriptions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "plan_id", null: false
    t.string "status", null: false
    t.integer "max_amount", null: false
    t.integer "initial_charge", default: 0
    t.string "pg_id", null: false
    t.string "pg_reference_id"
    t.string "auth_link", null: false
    t.json "pg_json"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "payment_gateway", default: "cashfree"
    t.index ["pg_id"], name: "index_subscriptions_on_pg_id"
    t.index ["pg_reference_id"], name: "index_subscriptions_on_pg_reference_id"
    t.index ["plan_id"], name: "index_subscriptions_on_plan_id"
    t.index ["user_id", "status"], name: "index_subscriptions_on_user_id_and_status"
    t.index ["user_id"], name: "index_subscriptions_on_user_id"
  end

  create_table "taggings", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "taggable_type", null: false
    t.bigint "taggable_id", null: false
    t.bigint "tag_id", null: false
    t.string "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tag_id"], name: "index_taggings_on_tag_id"
    t.index ["taggable_id", "taggable_type", "status"], name: "index_taggings_on_taggable_and_status"
    t.index ["taggable_id", "taggable_type", "tag_id"], name: "index_taggings_on_taggable_and_tag_id", unique: true
    t.index ["taggable_type", "taggable_id"], name: "index_taggings_on_taggable"
  end

  create_table "tags", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "identifier", limit: 50, null: false
    t.text "description", size: :tiny
    t.boolean "active", default: true
    t.string "tag_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["identifier", "tag_type", "active"], name: "index_tags_on_identifier_and_tag_type_and_active", unique: true
  end

  create_table "toasts", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.string "title"
    t.text "body"
    t.boolean "active"
    t.datetime "start_time", precision: nil
    t.datetime "end_time", precision: nil
    t.integer "max_user_sessions"
    t.bigint "admin_user_id"
    t.bigint "circle_id", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_toasts_on_admin_user_id"
    t.index ["circle_id"], name: "index_toasts_on_circle_id"
  end

  create_table "un_subscribed_words", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "word", null: false
    t.bigint "admin_user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["word"], name: "index_un_subscribed_words_on_word", unique: true
  end

  create_table "user_badges", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "badge_icon"
    t.string "badge_banner"
    t.string "badge_label"
    t.bigint "location_circle_id"
    t.bigint "political_party_id"
    t.boolean "active", default: true
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "badge_ring"
    t.boolean "is_celebrated", default: false
    t.bigint "badge_role_id"
    t.string "badge_text"
    t.integer "grade_level"
    t.boolean "marketing_consent", default: true
    t.index ["badge_role_id"], name: "index_user_badges_on_badge_role_id"
    t.index ["user_id", "active"], name: "index_user_badges_on_user_id_and_active"
    t.index ["user_id"], name: "index_user_badges_on_user_id"
  end

  create_table "user_circle_permission_groups", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "circle_id", null: false
    t.bigint "permission_group_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id"], name: "index_user_circle_permission_groups_on_circle_id"
    t.index ["permission_group_id"], name: "index_user_circle_permission_groups_on_permission_group_id"
    t.index ["user_id", "circle_id"], name: "index_circle_permission_groups_on_user_id_and_circle_id", unique: true
    t.index ["user_id"], name: "index_user_circle_permission_groups_on_user_id"
  end

  create_table "user_circles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "circle_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "source_of_join", limit: 1
    t.index ["circle_id"], name: "index_user_circles_on_circle_id"
    t.index ["user_id", "circle_id"], name: "index_user_circles_on_user_id_and_circle_id", unique: true
    t.index ["user_id"], name: "index_user_circles_on_user_id"
  end

  create_table "user_contact_suggestions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "phone", null: false
    t.bigint "phone_user_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["phone", "user_id"], name: "index_user_contact_suggestions_on_phone_and_user_id", unique: true
    t.index ["user_id"], name: "index_user_contact_suggestions_on_user_id"
  end

  create_table "user_contacts", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "contact_id"
    t.bigint "user_id"
    t.text "name", collation: "utf8mb4_unicode_ci"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["contact_id"], name: "index_user_contacts_on_contact_id"
    t.index ["user_id", "contact_id"], name: "index_user_contacts_on_user_id_and_contact_id", unique: true
    t.index ["user_id"], name: "index_user_contacts_on_user_id"
  end

  create_table "user_device_tokens", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "device_token"
    t.bigint "user_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "app_version"
    t.string "app_os"
    t.timestamp "uninstall_discovered_at"
    t.string "device_id"
    t.string "device_make"
    t.string "device_model"
    t.bigint "mapped_device_id"
    t.string "firebase_app_instance_id"
    t.index ["active", "device_token"], name: "index_user_device_tokens_on_active_and_device_token", length: { device_token: 255 }
    t.index ["active", "user_id", "device_token"], name: "index_user_device_tokens_on_active_and_user_id_and_device_token", length: { device_token: 255 }
    t.index ["device_id"], name: "index_user_device_tokens_on_device_id"
    t.index ["mapped_device_id"], name: "index_user_device_tokens_on_mapped_device_id"
    t.index ["user_id", "device_id"], name: "index_user_device_tokens_on_user_id_and_device_id"
    t.index ["user_id"], name: "index_user_device_tokens_on_user_id"
  end

  create_table "user_followers", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "follower_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "source_of_follow", limit: 1
    t.index ["created_at"], name: "index_user_followers_on_created_at"
    t.index ["follower_id"], name: "index_user_followers_on_follower_id"
    t.index ["user_id", "follower_id"], name: "index_user_followers_on_user_id_and_follower_id", unique: true
    t.index ["user_id"], name: "index_user_followers_on_user_id"
  end

  create_table "user_frames", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "frame_id", null: false
    t.string "status", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["frame_id"], name: "index_user_frames_on_frame_id"
    t.index ["user_id", "frame_id"], name: "index_user_frames_on_user_id_and_frame_id", unique: true
    t.index ["user_id", "status"], name: "index_user_frames_on_user_id_and_status"
    t.index ["user_id"], name: "index_user_frames_on_user_id"
  end

  create_table "user_group_members", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_group_id", null: false
    t.bigint "user_id", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_group_id", "user_id"], name: "index_user_group_members_on_user_group_id_and_user_id", unique: true
    t.index ["user_group_id"], name: "index_user_group_members_on_user_group_id"
    t.index ["user_id"], name: "index_user_group_members_on_user_id"
  end

  create_table "user_groups", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.bigint "user_id", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "max_trends", default: 10
    t.integer "min_trends", default: 6
    t.index ["name", "user_id"], name: "index_user_groups_on_name_and_user_id", unique: true
    t.index ["user_id"], name: "index_user_groups_on_user_id"
  end

  create_table "user_invites", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", collation: "utf8mb4_unicode_ci"
    t.bigint "phone"
    t.bigint "user_id", null: false
    t.boolean "invited", default: true
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "sent", default: false
    t.index ["created_at"], name: "index_user_invites_on_created_at"
    t.index ["phone", "user_id"], name: "index_user_invites_on_phone_and_user_id", unique: true
    t.index ["phone"], name: "index_user_invites_on_phone"
    t.index ["sent"], name: "index_user_invites_on_sent"
    t.index ["user_id"], name: "index_user_invites_on_user_id"
  end

  create_table "user_leader_photos", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "creator_type"
    t.bigint "creator_id"
    t.bigint "user_poster_layout_id", null: false
    t.string "photo_type"
    t.bigint "photo_id"
    t.bigint "user_id"
    t.bigint "circle_id"
    t.integer "header_type", limit: 1, null: false
    t.integer "priority", limit: 1
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "draft_data"
    t.index ["circle_id"], name: "index_user_leader_photos_on_circle_id"
    t.index ["creator_type", "creator_id"], name: "index_user_leader_photos_on_creator"
    t.index ["photo_type", "photo_id"], name: "index_user_leader_photos_on_photo"
    t.index ["user_id"], name: "index_user_leader_photos_on_user_id"
    t.index ["user_poster_layout_id"], name: "index_user_leader_photos_on_user_poster_layout_id"
  end

  create_table "user_metadata", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "key"
    t.text "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "key"], name: "index_user_metadata_on_user_id_and_key"
    t.index ["user_id"], name: "index_user_metadata_on_user_id"
  end

  create_table "user_nicknames", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "nickname_id"
    t.boolean "active"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["nickname_id"], name: "index_user_nicknames_on_nickname_id"
    t.index ["user_id"], name: "index_user_nicknames_on_user_id"
  end

  create_table "user_plan_extensions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "user_plan_id", null: false
    t.integer "duration_in_days", null: false
    t.string "reason", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "created_at"], name: "index_user_plan_extensions_on_user_id_and_created_at"
    t.index ["user_id"], name: "index_user_plan_extensions_on_user_id"
    t.index ["user_plan_id"], name: "index_user_plan_extensions_on_user_plan_id"
  end

  create_table "user_plan_logs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "plan_id", null: false
    t.datetime "start_date", null: false
    t.datetime "end_date", null: false
    t.string "entity_type", null: false
    t.bigint "entity_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "active", default: true
    t.index ["entity_type", "entity_id"], name: "index_user_plan_logs_on_entity"
    t.index ["plan_id"], name: "index_user_plan_logs_on_plan_id"
    t.index ["user_id"], name: "index_user_plan_logs_on_user_id"
  end

  create_table "user_plans", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "plan_id", null: false
    t.integer "amount", null: false
    t.datetime "end_date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "source"
    t.index ["plan_id"], name: "index_user_plans_on_plan_id"
    t.index ["user_id"], name: "index_user_plans_on_user_id"
    t.index ["user_id"], name: "unique_index_for_user_plans_on_user_id", unique: true
  end

  create_table "user_points_ledgers", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "points"
    t.string "action"
    t.integer "action_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "reference_type"
    t.integer "reference_id"
    t.datetime "actioned_at", precision: nil
    t.index ["user_id"], name: "index_user_points_ledgers_on_user_id"
  end

  create_table "user_poll_options", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "poll_id"
    t.bigint "poll_option_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["poll_id"], name: "index_user_poll_options_on_poll_id"
    t.index ["poll_option_id"], name: "index_user_poll_options_on_poll_option_id"
    t.index ["user_id", "poll_id"], name: "index_user_poll_options_on_user_id_and_poll_id", unique: true
    t.index ["user_id"], name: "index_user_poll_options_on_user_id"
  end

  create_table "user_poster_layouts", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.integer "h1_count", default: 0
    t.integer "h2_count", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "active", default: true
    t.string "entity_type", null: false
    t.bigint "entity_id", null: false
    t.integer "se_user_id"
    t.string "h1_background_type", default: "transparent"
    t.string "h2_background_type", default: "transparent"
    t.integer "quality_score", limit: 1
    t.string "review_status", default: "awaited", null: false
    t.string "quality_score_reason"
    t.datetime "first_activated_at"
    t.text "video_frame_protocol_photo_url"
    t.index ["entity_id", "entity_type"], name: "index_on_entity_type_entity_id", unique: true
    t.index ["user_id"], name: "index_user_poster_layouts_on_user_id"
  end

  create_table "user_poster_photos", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "pose", null: false
    t.string "photo_type", null: false
    t.bigint "photo_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["photo_type", "photo_id"], name: "index_user_poster_photos_on_photo"
    t.index ["user_id", "pose"], name: "index_user_poster_photos_on_user_id_and_pose", unique: true
    t.index ["user_id"], name: "index_user_poster_photos_on_user_id"
  end

  create_table "user_product_subscription_extensions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "product_id", null: false
    t.string "subscription_type", null: false
    t.bigint "order_id"
    t.integer "duration_in_days", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_user_product_subscription_extensions_on_order_id"
    t.index ["product_id"], name: "index_user_product_subscription_extensions_on_product_id"
    t.index ["user_id"], name: "index_user_product_subscription_extensions_on_user_id"
  end

  create_table "user_product_subscriptions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "order_id"
    t.bigint "user_id", null: false
    t.datetime "start_date", precision: nil
    t.datetime "end_date", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "item_type"
    t.bigint "item_id"
    t.boolean "active", default: true
    t.string "source", default: "orders"
    t.index ["item_type", "item_id"], name: "index_user_product_subscriptions_on_item"
    t.index ["order_id"], name: "index_user_product_subscriptions_on_order_id"
    t.index ["user_id"], name: "index_user_product_subscriptions_on_user_id"
  end

  create_table "user_professions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "profession_id", null: false
    t.bigint "sub_profession_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["profession_id"], name: "index_user_professions_on_profession_id"
    t.index ["sub_profession_id"], name: "index_user_professions_on_sub_profession_id"
    t.index ["user_id", "profession_id"], name: "index_user_professions_on_user_id_and_profession_id", unique: true
    t.index ["user_id"], name: "index_user_professions_on_user_id"
  end

  create_table "user_recommended_frames", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "frame_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["frame_id"], name: "index_user_recommended_frames_on_frame_id"
    t.index ["user_id", "frame_id"], name: "index_user_recommended_frames_on_user_id_and_frame_id", unique: true
    t.index ["user_id"], name: "index_user_recommended_frames_on_user_id"
  end

  create_table "user_referrals", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "referred_user_id", null: false
    t.string "status", default: "created", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["referred_user_id"], name: "index_user_referrals_on_referred_user_id"
    t.index ["referred_user_id"], name: "unique_index_on_referred_user_id", unique: true
    t.index ["user_id"], name: "index_user_referrals_on_user_id"
  end

  create_table "user_roles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "role_id", null: false
    t.bigint "primary_circle_id"
    t.bigint "secondary_circle_id"
    t.boolean "badge_ring"
    t.string "badge_color"
    t.integer "grade_level"
    t.boolean "primary_role"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_celebrated", default: false
    t.boolean "badge_icon_ribbon"
    t.date "start_date", default: "1900-01-01", null: false
    t.date "end_date", default: "9999-12-31", null: false
    t.boolean "show_on_about_page", default: false
    t.bigint "purview_circle_id"
    t.integer "badge_icon_id"
    t.bigint "parent_circle_id"
    t.string "display_name_order", default: "", null: false
    t.string "free_text"
    t.string "verification_status", default: "verified"
    t.string "proof_link"
    t.boolean "is_self_claimed", default: false
    t.boolean "is_letter_pending", default: false
    t.index ["active", "verification_status"], name: "index_user_roles_on_active_and_verification_status"
    t.index ["active"], name: "index_user_roles_on_active"
    t.index ["grade_level"], name: "index_user_roles_on_grade_level"
    t.index ["parent_circle_id"], name: "index_user_roles_on_parent_circle_id"
    t.index ["primary_role"], name: "index_user_roles_on_primary_role"
    t.index ["purview_circle_id"], name: "index_user_roles_on_purview_circle_id"
    t.index ["role_id"], name: "index_user_roles_on_role_id"
    t.index ["user_id", "role_id"], name: "index_user_roles_on_user_id_and_role_id"
    t.index ["user_id"], name: "index_user_roles_on_user_id"
  end

  create_table "user_stories", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "circle_story_thread_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "circle_id"
    t.index ["circle_story_thread_id"], name: "index_user_stories_on_circle_story_thread_id"
    t.index ["user_id", "circle_story_thread_id"], name: "index_user_stories_on_user_id_and_circle_story_thread_id", unique: true
    t.index ["user_id"], name: "index_user_stories_on_user_id"
  end

  create_table "user_story_photo_likes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "user_story_photo_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id", "user_story_photo_id"], name: "index_user_story_photo_likes_on_user_id_and_user_story_photo_id", unique: true
    t.index ["user_id"], name: "index_user_story_photo_likes_on_user_id"
    t.index ["user_story_photo_id"], name: "index_user_story_photo_likes_on_user_story_photo_id"
  end

  create_table "user_story_photos", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_story_id"
    t.bigint "photo_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.text "comment", collation: "utf8mb4_bin"
    t.index ["photo_id"], name: "index_user_story_photos_on_photo_id"
    t.index ["user_story_id"], name: "index_user_story_photos_on_user_story_id"
  end

  create_table "user_story_photos_views", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "user_story_photo_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id", "user_story_photo_id"], name: "index_user_story_photos_views_on_user_id_and_user_story_photo_id", unique: true
    t.index ["user_id"], name: "index_user_story_photos_views_on_user_id"
    t.index ["user_story_photo_id"], name: "index_user_story_photos_views_on_user_story_photo_id"
  end

  create_table "user_toast_closes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "toast_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["toast_id"], name: "index_user_toast_closes_on_toast_id"
    t.index ["user_id"], name: "index_user_toast_closes_on_user_id"
  end

  create_table "user_token_usages", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_token_id"
    t.string "app_version"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "user_id"
    t.index ["user_id"], name: "index_user_token_usages_on_user_id"
    t.index ["user_token_id"], name: "index_user_token_usages_on_user_token_id"
  end

  create_table "user_tokens", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "access_token"
    t.bigint "user_id"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "app_version"
    t.string "app_os"
    t.integer "refer_user_id"
    t.index ["access_token"], name: "index_user_tokens_on_access_token", unique: true
    t.index ["user_id"], name: "index_user_tokens_on_user_id"
  end

  create_table "user_video_frames", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "video_frame_id", null: false
    t.text "identity_photo_url", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "video_frame_id", "active"], name: "idx_on_user_id_video_frame_id_active_4d02a3d843", unique: true
    t.index ["user_id"], name: "index_user_video_frames_on_user_id"
    t.index ["video_frame_id"], name: "index_user_video_frames_on_video_frame_id"
  end

  create_table "user_video_posters", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "source_video_id", null: false
    t.bigint "user_video_frame_id", null: false
    t.bigint "user_id", null: false
    t.bigint "generated_video_id"
    t.string "status", null: false
    t.string "job_id", null: false
    t.json "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "active", default: true
    t.index ["generated_video_id"], name: "index_user_video_posters_on_generated_video_id"
    t.index ["job_id"], name: "index_user_video_posters_on_job_id", unique: true
    t.index ["source_video_id", "user_video_frame_id", "active"], name: "idx_on_source_video_id_user_video_frame_id_active_001e5fd50e"
    t.index ["source_video_id"], name: "index_user_video_posters_on_source_video_id"
    t.index ["user_id"], name: "index_user_video_posters_on_user_id"
    t.index ["user_video_frame_id"], name: "index_user_video_posters_on_user_video_frame_id"
  end

  create_table "user_wati_campaigns", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "broadcast_name"
    t.json "campaign_data"
    t.datetime "campaign_date"
    t.string "campaign_type"
    t.string "status", null: false
    t.string "template_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_wati_campaigns_on_user_id"
  end

  create_table "users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", limit: 191, collation: "utf8mb4_unicode_ci"
    t.string "email"
    t.bigint "phone"
    t.boolean "active", default: true
    t.boolean "verified", default: false
    t.integer "photo_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "signed_up", default: false
    t.text "short_bio", collation: "utf8mb4_unicode_ci"
    t.date "dob"
    t.integer "gender"
    t.boolean "show_phone", default: false
    t.string "mfa_secret"
    t.boolean "internal", default: false
    t.string "dynamic_link"
    t.integer "refer_user_id"
    t.string "profile_dynamic_link"
    t.integer "total_followers_count", default: 0
    t.integer "total_following_count", default: 0
    t.boolean "internal_journalist", default: false
    t.string "invite_card"
    t.bigint "village_id"
    t.bigint "mandal_id"
    t.bigint "district_id"
    t.bigint "mla_constituency_id"
    t.bigint "mp_constituency_id"
    t.bigint "state_id"
    t.datetime "internal_journalist_enabled_at", precision: nil
    t.string "app_version"
    t.boolean "marketing_consent", default: true
    t.bigint "affiliated_party_circle_id"
    t.integer "app_build_number", default: 10, unsigned: true
    t.bigint "birth_place_id"
    t.string "education", collation: "utf8mb4_unicode_ci"
    t.text "office_address", collation: "utf8mb4_unicode_ci"
    t.string "contact_email"
    t.bigint "contact_phone"
    t.string "poster_photo_type"
    t.bigint "poster_photo_id"
    t.string "poster_photo_with_background_type"
    t.bigint "poster_photo_with_background_id"
    t.string "family_frame_photo_type"
    t.bigint "family_frame_photo_id"
    t.string "family_frame_name", collation: "utf8mb4_unicode_ci"
    t.string "hero_frame_photo_type"
    t.bigint "hero_frame_photo_id"
    t.string "custom_role_name", collation: "utf8mb4_unicode_ci"
    t.string "status", default: "pre_signup"
    t.integer "unread_notifications_count"
    t.integer "posts_count"
    t.string "video_poster_default_photo_type"
    t.bigint "video_poster_default_photo_id"
    t.index ["active"], name: "index_users_on_active"
    t.index ["district_id"], name: "index_users_on_district_id"
    t.index ["family_frame_photo_type", "family_frame_photo_id"], name: "index_users_on_family_frame_photo"
    t.index ["hero_frame_photo_type", "hero_frame_photo_id"], name: "index_users_on_hero_frame_photo"
    t.index ["mandal_id"], name: "index_users_on_mandal_id"
    t.index ["mla_constituency_id"], name: "index_users_on_mla_constituency_id"
    t.index ["mp_constituency_id"], name: "index_users_on_mp_constituency_id"
    t.index ["phone"], name: "index_users_on_phone", unique: true
    t.index ["poster_photo_type", "poster_photo_id"], name: "index_users_on_poster_photo"
    t.index ["poster_photo_with_background_type", "poster_photo_with_background_id"], name: "index_users_on_poster_photo_with_background"
    t.index ["status"], name: "index_users_on_status"
    t.index ["total_followers_count"], name: "index_users_on_total_followers_count"
    t.index ["video_poster_default_photo_type", "video_poster_default_photo_id"], name: "index_users_on_video_poster_default_photo"
  end

  create_table "versions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "item_type", limit: 191, null: false
    t.bigint "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.text "object", size: :long
    t.datetime "created_at", precision: nil
    t.text "object_changes", size: :long
    t.string "tag"
    t.index ["item_type", "item_id", "id"], name: "index_versions_on_item_type_and_item_id_and_id"
    t.index ["item_type", "item_id", "tag", "id"], name: "index_versions_on_item_type_item_id_tag_and_id_desc", order: { id: :desc }
    t.index ["item_type", "item_id", "tag"], name: "index_versions_on_item_type_and_item_id_and_tag"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  create_table "video_creative_circles", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "video_creative_id", null: false
    t.bigint "circle_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["circle_id"], name: "index_video_creative_circles_on_circle_id"
    t.index ["video_creative_id"], name: "index_video_creative_circles_on_video_creative_id"
  end

  create_table "video_creative_views", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "video_creative_id", null: false
    t.bigint "user_id", null: false
    t.datetime "viewed_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "id"], name: "index_video_creative_views_on_user_id_and_id_desc", order: { id: :desc }
    t.index ["user_id"], name: "index_video_creative_views_on_user_id"
    t.index ["video_creative_id"], name: "index_video_creative_views_on_video_creative_id"
  end

  create_table "video_creatives", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "kind", null: false
    t.boolean "active", default: true
    t.datetime "start_time", null: false
    t.datetime "end_time", null: false
    t.bigint "event_id"
    t.string "creator_type", null: false
    t.bigint "creator_id", null: false
    t.bigint "designer_id"
    t.bigint "video_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "caption"
    t.index ["creator_type", "creator_id"], name: "index_video_creatives_on_creator"
    t.index ["designer_id"], name: "index_video_creatives_on_designer_id"
    t.index ["event_id"], name: "index_video_creatives_on_event_id"
    t.index ["start_time", "end_time", "active"], name: "index_video_creatives_on_start_time_and_end_time_and_active"
    t.index ["video_id"], name: "index_video_creatives_on_video_id"
  end

  create_table "video_frame_views", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "video_frame_id", null: false
    t.bigint "user_id", null: false
    t.datetime "viewed_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_video_frame_views_on_user_id"
    t.index ["video_frame_id"], name: "index_video_frame_views_on_video_frame_id"
  end

  create_table "video_frames", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "video_type", null: false
    t.bigint "font_id", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["font_id"], name: "index_video_frames_on_font_id"
    t.index ["video_type", "font_id"], name: "index_video_frames_on_video_type_and_font_id", unique: true
  end

  create_table "video_poster_shares", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "video_creative_id", null: false
    t.bigint "user_id", null: false
    t.bigint "video_frame_id", null: false
    t.bigint "user_video_poster_id", null: false
    t.datetime "actioned_at", null: false
    t.string "method"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_video_poster_shares_on_user_id"
    t.index ["user_video_poster_id"], name: "index_video_poster_shares_on_user_video_poster_id"
    t.index ["video_creative_id"], name: "index_video_poster_shares_on_video_creative_id"
    t.index ["video_frame_id"], name: "index_video_poster_shares_on_video_frame_id"
  end

  create_table "videos", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.text "url"
    t.text "thumbnail_url"
    t.bigint "user_id"
    t.string "user_type", default: "User"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.text "source_url"
    t.string "hash_key"
    t.string "status", default: "processed"
    t.string "path"
    t.string "thumbnail_path"
    t.string "service", null: false
    t.float "bitrate"
    t.integer "duration", limit: 2
    t.integer "width"
    t.integer "height"
    t.string "mode"
    t.index ["hash_key"], name: "index_videos_on_hash_key"
    t.index ["user_id", "user_type"], name: "index_videos_on_user_id_and_user_type"
    t.index ["user_id"], name: "index_videos_on_user_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "admin_media", "admin_users"
  add_foreign_key "admin_users", "admin_media"
  add_foreign_key "badge_icon_groups", "circles"
  add_foreign_key "badge_icons", "admin_media"
  add_foreign_key "badge_icons", "badge_icon_groups"
  add_foreign_key "campaigns", "admin_media"
  add_foreign_key "circle_frames", "circles"
  add_foreign_key "circle_frames", "frames"
  add_foreign_key "circle_monthly_usages", "circles"
  add_foreign_key "circle_package_mappings", "circle_packages"
  add_foreign_key "circle_package_mappings", "circles"
  add_foreign_key "circle_photos", "circles"
  add_foreign_key "circle_photos", "photos"
  add_foreign_key "circle_premium_interests", "circles"
  add_foreign_key "circle_premium_interests", "users"
  add_foreign_key "circle_story_threads", "circles"
  add_foreign_key "circles", "admin_users", column: "relationship_manager_id"
  add_foreign_key "circles", "photos"
  add_foreign_key "event_circles", "circles"
  add_foreign_key "event_circles", "events"
  add_foreign_key "excluded_user_circle_permissions", "circles"
  add_foreign_key "excluded_user_circle_permissions", "users"
  add_foreign_key "frame_recommendations", "frames"
  add_foreign_key "frame_views", "frames"
  add_foreign_key "frame_views", "users"
  add_foreign_key "frames", "fonts"
  add_foreign_key "garuda_notifications", "circles", column: "location_circle_id"
  add_foreign_key "garuda_notifications", "circles", column: "party_circle_id"
  add_foreign_key "hashtag_circles", "circles"
  add_foreign_key "hashtag_circles", "hashtags"
  add_foreign_key "hashtag_likes", "hashtags"
  add_foreign_key "hashtag_likes", "users"
  add_foreign_key "internal_role_permissions", "internal_permissions"
  add_foreign_key "internal_role_permissions", "internal_permissions"
  add_foreign_key "internal_role_permissions", "internal_roles"
  add_foreign_key "internal_role_permissions", "internal_roles"
  add_foreign_key "internal_roles", "circles"
  add_foreign_key "internal_roles", "circles"
  add_foreign_key "internal_user_roles", "internal_roles"
  add_foreign_key "internal_user_roles", "internal_roles"
  add_foreign_key "internal_user_roles", "users"
  add_foreign_key "internal_user_roles", "users"
  add_foreign_key "invoices", "users"
  add_foreign_key "notifications", "users"
  add_foreign_key "one_time_passwords", "users"
  add_foreign_key "order_items", "item_prices"
  add_foreign_key "order_items", "order_items", column: "parent_order_item_id"
  add_foreign_key "order_items", "orders"
  add_foreign_key "order_transactions", "orders"
  add_foreign_key "orders", "plans"
  add_foreign_key "orders", "users"
  add_foreign_key "photo_labels", "photos"
  add_foreign_key "photos", "users"
  add_foreign_key "plan_products", "plans"
  add_foreign_key "plan_products", "products"
  add_foreign_key "poll_options", "polls"
  add_foreign_key "polls", "posts"
  add_foreign_key "post_bounces", "circles"
  add_foreign_key "post_bounces", "posts"
  add_foreign_key "post_circles", "circles"
  add_foreign_key "post_circles", "posts"
  add_foreign_key "post_comments", "posts"
  add_foreign_key "post_comments", "users"
  add_foreign_key "post_hashtags", "hashtags"
  add_foreign_key "post_hashtags", "posts"
  add_foreign_key "post_likes", "posts"
  add_foreign_key "post_likes", "users"
  add_foreign_key "post_photos", "photos"
  add_foreign_key "post_photos", "posts"
  add_foreign_key "post_reports", "posts"
  add_foreign_key "post_reports", "users"
  add_foreign_key "post_user_tags", "posts"
  add_foreign_key "post_user_tags", "users"
  add_foreign_key "post_videos", "posts"
  add_foreign_key "post_videos", "videos"
  add_foreign_key "poster_creative_circles", "circles"
  add_foreign_key "poster_creative_circles", "poster_creatives"
  add_foreign_key "poster_creative_views", "poster_creatives"
  add_foreign_key "poster_creative_views", "users"
  add_foreign_key "poster_creatives", "events"
  add_foreign_key "poster_layout_remarks", "admin_users"
  add_foreign_key "poster_layout_remarks", "user_poster_layouts"
  add_foreign_key "poster_photos", "posters"
  add_foreign_key "poster_shares", "frames"
  add_foreign_key "poster_shares", "poster_creatives"
  add_foreign_key "poster_shares", "users"
  add_foreign_key "posts", "users"
  add_foreign_key "premium_pitches", "users"
  add_foreign_key "professions", "admin_media"
  add_foreign_key "profile_views", "users"
  add_foreign_key "profile_views", "users", column: "viewer_id"
  add_foreign_key "roles", "badge_icon_groups"
  add_foreign_key "singular_data", "users"
  add_foreign_key "sub_professions", "professions"
  add_foreign_key "subscription_charge_refunds", "subscription_charges"
  add_foreign_key "subscription_charge_refunds", "users"
  add_foreign_key "subscription_charges", "subscriptions"
  add_foreign_key "subscription_charges", "users"
  add_foreign_key "subscriptions", "plans"
  add_foreign_key "subscriptions", "users"
  add_foreign_key "taggings", "tags"
  add_foreign_key "user_badges", "badge_roles"
  add_foreign_key "user_badges", "users"
  add_foreign_key "user_circles", "circles"
  add_foreign_key "user_circles", "users"
  add_foreign_key "user_contact_suggestions", "users"
  add_foreign_key "user_device_tokens", "devices", column: "mapped_device_id"
  add_foreign_key "user_device_tokens", "users"
  add_foreign_key "user_frames", "frames"
  add_foreign_key "user_frames", "users"
  add_foreign_key "user_group_members", "user_groups"
  add_foreign_key "user_group_members", "users"
  add_foreign_key "user_groups", "users"
  add_foreign_key "user_invites", "users"
  add_foreign_key "user_leader_photos", "user_poster_layouts"
  add_foreign_key "user_metadata", "users"
  add_foreign_key "user_nicknames", "nicknames"
  add_foreign_key "user_nicknames", "users"
  add_foreign_key "user_plan_extensions", "user_plans"
  add_foreign_key "user_plan_extensions", "users"
  add_foreign_key "user_plan_logs", "plans"
  add_foreign_key "user_plan_logs", "users"
  add_foreign_key "user_plans", "plans"
  add_foreign_key "user_plans", "users"
  add_foreign_key "user_points_ledgers", "users"
  add_foreign_key "user_poll_options", "poll_options"
  add_foreign_key "user_poll_options", "polls"
  add_foreign_key "user_poll_options", "users"
  add_foreign_key "user_poster_layouts", "users"
  add_foreign_key "user_poster_photos", "users"
  add_foreign_key "user_product_subscription_extensions", "orders"
  add_foreign_key "user_product_subscription_extensions", "products"
  add_foreign_key "user_product_subscription_extensions", "users"
  add_foreign_key "user_product_subscriptions", "orders"
  add_foreign_key "user_product_subscriptions", "users"
  add_foreign_key "user_professions", "professions"
  add_foreign_key "user_professions", "sub_professions"
  add_foreign_key "user_professions", "users"
  add_foreign_key "user_recommended_frames", "frames"
  add_foreign_key "user_recommended_frames", "users"
  add_foreign_key "user_referrals", "users"
  add_foreign_key "user_referrals", "users", column: "referred_user_id"
  add_foreign_key "user_stories", "circle_story_threads"
  add_foreign_key "user_stories", "users"
  add_foreign_key "user_story_photo_likes", "user_story_photos"
  add_foreign_key "user_story_photo_likes", "users"
  add_foreign_key "user_story_photos", "photos"
  add_foreign_key "user_story_photos", "user_stories"
  add_foreign_key "user_story_photos_views", "user_story_photos"
  add_foreign_key "user_story_photos_views", "users"
  add_foreign_key "user_token_usages", "user_tokens"
  add_foreign_key "user_token_usages", "users"
  add_foreign_key "user_tokens", "users"
  add_foreign_key "user_video_frames", "users"
  add_foreign_key "user_video_frames", "video_frames"
  add_foreign_key "user_video_posters", "user_video_frames"
  add_foreign_key "user_video_posters", "users"
  add_foreign_key "user_video_posters", "videos", column: "generated_video_id"
  add_foreign_key "user_video_posters", "videos", column: "source_video_id"
  add_foreign_key "user_wati_campaigns", "users"
  add_foreign_key "video_creative_circles", "circles"
  add_foreign_key "video_creative_circles", "video_creatives"
  add_foreign_key "video_creative_views", "users"
  add_foreign_key "video_creative_views", "video_creatives"
  add_foreign_key "video_creatives", "admin_users", column: "designer_id"
  add_foreign_key "video_creatives", "events"
  add_foreign_key "video_creatives", "videos"
  add_foreign_key "video_frame_views", "users"
  add_foreign_key "video_frame_views", "video_frames"
  add_foreign_key "video_frames", "fonts"
  add_foreign_key "video_poster_shares", "user_video_posters"
  add_foreign_key "video_poster_shares", "users"
  add_foreign_key "video_poster_shares", "video_creatives"
  add_foreign_key "video_poster_shares", "video_frames"
end
