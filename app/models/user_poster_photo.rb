class UserPosterPhoto < ApplicationRecord
  belongs_to :user
  belongs_to :photo, polymorphic: true

  validates :pose, presence: true
  validates :user_id, uniqueness: { scope: :pose }

  # Ensure photo is either a Photo or AdminMedium
  validates :photo, presence: true
  validate :photo_must_be_valid_type

  private

  def photo_must_be_valid_type
    return if photo.nil?

    unless photo.is_a?(Photo) || photo.is_a?(AdminMedium)
      errors.add(:photo, 'must be either a Photo or AdminMedium')
    end
  end
end
