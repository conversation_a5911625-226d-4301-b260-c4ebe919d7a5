require 'rails_helper'

RSpec.describe CashfreeController, type: :controller do
  describe 'POST #subscription_callback' do
    let(:subscription) { create(:subscription, pg_reference_id: 'sub_ref_123') }

    context 'when required fields are missing' do
      it 'notifies Honeybadger and returns invalid callback message' do
        allow(Honeybad<PERSON>).to receive(:notify)
        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_STATUS_CHANGE' }
        expect(Honeybadger).to have_received(:notify).with('Invalid cashfree subscription callback', context: { params: anything })
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('Invalid callback')
      end
    end

    context 'when subscription is not found by pg_reference_id' do
      it 'tries to find subscription by pg_id when cf_subscriptionId is present' do
        allow(Honeybadger).to receive(:notify)
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(true)
        
        # Create subscription with pg_id but different pg_reference_id
        subscription_with_pg_id = create(:subscription, pg_id: 'pg_123', pg_reference_id: 'different_ref')
        
        post :subscription_callback, params: { 
          cf_event: 'SUBSCRIPTION_STATUS_CHANGE', 
          cf_subReferenceId: 'non_existent_ref',
          cf_subscriptionId: 'pg_123',
          signature: 'cf_signature' 
        }
        
        expect(response).to have_http_status(:ok)
        expect(Honeybadger).not_to have_received(:notify).with('Subscription not found', context: { params: anything })
      end

      it 'notifies Honeybadger when subscription is not found by either pg_reference_id or pg_id' do
        allow(Honeybadger).to receive(:notify)
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(true)
        
        post :subscription_callback, params: { 
          cf_event: 'SUBSCRIPTION_STATUS_CHANGE', 
          cf_subReferenceId: 'non_existent_ref',
          cf_subscriptionId: 'non_existent_pg_id',
          signature: 'cf_signature' 
        }
        
        expect(Honeybadger).to have_received(:notify).with('Subscription not found', context: { params: anything })
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('Subscription not found')
      end

      it 'notifies Honeybadger when subscription is not found and cf_subscriptionId is blank' do
        allow(Honeybadger).to receive(:notify)
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(true)
        
        post :subscription_callback, params: { 
          cf_event: 'SUBSCRIPTION_STATUS_CHANGE', 
          cf_subReferenceId: 'non_existent_ref',
          cf_subscriptionId: '',
          signature: 'cf_signature' 
        }
        
        expect(Honeybadger).to have_received(:notify).with('Subscription not found', context: { params: anything })
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('Subscription not found')
      end
    end

    context 'when subscription is found by pg_reference_id' do
      it 'processes the callback normally without trying pg_id lookup' do
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(true)
        @subscription_charge = FactoryBot.create(:subscription_charge, status: :success, subscription: subscription)
        
        post :subscription_callback, params: { 
          cf_event: 'SUBSCRIPTION_STATUS_CHANGE', 
          cf_subReferenceId: subscription.pg_reference_id,
          cf_subscriptionId: 'different_pg_id',
          signature: 'cf_signature',
          cf_status: 'ACTIVE' 
        }
        
        subscription.reload
        expect(subscription.status).to eq('active')
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when subscription is not found' do
      it 'notifies Honeybadger and returns subscription not found message' do
        allow(Honeybadger).to receive(:notify)
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(true)
        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_STATUS_CHANGE', cf_subReferenceId: 'invalid_ref',
                                               signature: 'cf_signature' }
        expect(Honeybadger).to have_received(:notify).with('Subscription not found', context: { params: anything })
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('Subscription not found')
      end
    end

    context 'when event is SUBSCRIPTION_STATUS_CHANGE' do
      before do
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(true)
        @subscription_charge = FactoryBot.create(:subscription_charge, status: :success, subscription: subscription)
      end

      it 'resumes the subscription if status is ACTIVE' do
        subscription.pause!
        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_STATUS_CHANGE', signature: 'cf_signature',
                                               cf_subReferenceId: subscription.pg_reference_id, cf_status: 'ACTIVE' }
        subscription.reload
        expect(subscription.status).to eq('active')
        expect(response).to have_http_status(:ok)
      end

      it 'cancels the subscription if status is CUSTOMER_CANCELLED' do
        allow(subscription).to receive(:customer_cancel!)
        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_STATUS_CHANGE', signature: 'cf_signature',
                                               cf_subReferenceId: subscription.pg_reference_id,
                                               cf_status: 'CUSTOMER_CANCELLED' }
        subscription.reload
        expect(subscription.status).to eq('cancelled')
        expect(response).to have_http_status(:ok)
      end

      it 'pauses the subscription if status is CUSTOMER_PAUSED' do
        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_STATUS_CHANGE', signature: 'cf_signature',
                                               cf_subReferenceId: subscription.pg_reference_id,
                                               cf_status: 'CUSTOMER_PAUSED' }
        subscription.reload
        expect(subscription.status).to eq('cancelled')
        expect(response).to have_http_status(:ok)
      end

      it 'closes the subscription if status is LINK_EXPIRED' do
        subscription_1 = FactoryBot.create(:subscription, pg_reference_id: Faker::Lorem.unique.word, status: :created)
        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_STATUS_CHANGE', signature: 'cf_signature',
                                               cf_subReferenceId: subscription_1.pg_reference_id,
                                               cf_status: 'LINK_EXPIRED' }
        subscription_1.reload
        expect(subscription_1.status).to eq('closed')
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when event is SUBSCRIPTION_NEW_PAYMENT' do
      let(:subscription_charge) { create(:subscription_charge, subscription: subscription, pg_id: 'pg_123') }

      before do
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(true)
        allow(subscription).to receive(:subscription_charges).and_return([subscription_charge])
        allow(subscription_charge).to receive(:success)
        allow(subscription_charge).to receive(:save!)
        allow(subscription_charge).to receive(:is_user_has_premium_for_last_3_months).and_return(true)
      end

      it 'marks the auth charge as success if cf order id includes auth' do
        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_NEW_PAYMENT', signature: 'cf_signature',
                                               cf_subReferenceId: subscription.pg_reference_id,
                                               cf_paymentId: 'payment_123', cf_orderId: 'SUB_123_AUTH_24491871' }
        subscription_charge.reload
        expect(subscription_charge.status).to eq('success')
        expect(response).to have_http_status(:ok)
      end

      it 'marks the subscription charge as success if pg_id is present' do
        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_NEW_PAYMENT', signature: 'cf_signature',
                                               cf_subReferenceId: subscription.pg_reference_id,
                                               cf_merchantTxnId: 'pg_123', cf_orderId: 'SUB_123' }
        subscription_charge.reload
        expect(subscription_charge.status).to eq('success')
        expect(response).to have_http_status(:ok)
      end

      it 'when subscription charge is not found' do
        allow(Honeybadger).to receive(:notify)
        allow(subscription).to receive(:subscription_charges).and_return(nil)
        create(:subscription_charge, subscription: subscription, pg_id: 'pg_1234', charge_date: 1.days.ago)

        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_NEW_PAYMENT', signature: 'cf_signature',
                                               cf_subReferenceId: subscription.pg_reference_id,
                                               cf_merchantTxnId: 'pg_456', cf_orderId: 'SUB_123' }
        expect(Honeybadger).to have_received(:notify).with('Subscription charge not found', context: { params: anything })
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('Subscription charge not found')
      end
    end

    context 'when event is SUBSCRIPTION_PAYMENT_DECLINED' do
      let(:subscription_charge) { create(:subscription_charge, status: :created, subscription: subscription,
                                         pg_id: 'pg_123') }

      before do
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(true)
        allow(CashfreePaymentUtils).to receive(:cashfree_put_v1).and_return(nil)
        allow(subscription).to receive(:subscription_charges).and_return([subscription_charge])
        allow(subscription_charge).to receive(:fail!)
      end

      it 'marks the subscription charge as failed' do
        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_PAYMENT_DECLINED',
                                               cf_subReferenceId: subscription.pg_reference_id,
                                               cf_merchantTxnId: 'pg_123', signature: 'cf_signature',
                                               cf_retryAttempts: Constants.duration_days_after_premium_end_date + 1 }
        subscription_charge.reload
        expect(subscription_charge.status).to eq('failed')
        expect(response).to have_http_status(:ok)
      end

      it 'when subscription charge is not found' do
        allow(Honeybadger).to receive(:notify)
        allow(subscription).to receive(:subscription_charges).and_return(nil)
        post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_PAYMENT_DECLINED', signature: 'cf_signature',
                                               cf_subReferenceId: subscription.pg_reference_id,
                                               cf_merchantTxnId: 'pg_456' }
        expect(Honeybadger).to have_received(:notify).with('Subscription charge not found',
                                                           context: { params: anything })
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['message']).to eq('Subscription charge not found')
      end
    end

    context 'when event is SUBSCRIPTION_REFUND_STATUS' do
      let(:subscription_charge) { create(:subscription_charge, status: :success, subscription: subscription,
                                         pg_reference_id: 'payment_123') }

      before do
        allow(ActiveSupport::SecurityUtils).to receive(:secure_compare).and_return(true)
        allow(CashfreePaymentUtils).to receive(:cashfree_put_v1).and_return(nil)
        allow(subscription_charge).to receive(:refund_complete)
        allow(subscription_charge).to receive(:save!)
      end

      context 'with SubscriptionChargeRefund' do
        it 'marks the refund as success if refund status is SUCCESS and merchantRefundId contains refund ID' do
          # Create a mock refund
          refund = instance_double(SubscriptionChargeRefund, id: 123, status: 'initiated', may_mark_as_success?: true)
          allow(refund).to receive(:mark_as_success!)
          allow(refund).to receive(:update)

          # Mock the find_by to return our mock refund
          allow(SubscriptionChargeRefund).to receive(:find_by).with(id: "123").and_return(refund)

          post :subscription_callback, params: {
            cf_event: 'SUBSCRIPTION_REFUND_STATUS',
            cf_subReferenceId: subscription.pg_reference_id,
            cf_merchant_refund_id: "refund-123-#{subscription_charge.pg_id}",
            cf_refund_status: 'SUCCESS'
          }

          expect(refund).to have_received(:mark_as_success!)
          expect(response).to have_http_status(:ok)
        end

        it 'does not mark the refund as success if refund status is not SUCCESS' do
          # Create a mock refund
          refund = instance_double(SubscriptionChargeRefund, id: 123, status: 'initiated')
          allow(refund).to receive(:mark_as_success!)
          allow(refund).to receive(:update)
          allow(refund).to receive(:may_mark_as_failed?).and_return(true)
          allow(refund).to receive(:mark_as_failed!)

          # Mock the find_by to return our mock refund
          allow(SubscriptionChargeRefund).to receive(:find_by).with(id: "123").and_return(refund)

          post :subscription_callback, params: {
            cf_event: 'SUBSCRIPTION_REFUND_STATUS',
            cf_subReferenceId: subscription.pg_reference_id,
            cf_merchant_refund_id: "refund-123-#{subscription_charge.pg_id}",
            cf_refund_status: 'FAILED'
          }

          expect(refund).not_to have_received(:mark_as_success!)
          expect(refund).to have_received(:may_mark_as_failed?)
          expect(refund).to have_received(:mark_as_failed!)
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context 'with automatic refund' do
        it 'creates a new SubscriptionChargeRefund record for automatic refunds' do
          # Create a real subscription and subscription charge for this test
          test_subscription = create(:subscription, payment_gateway: 'cashfree', pg_reference_id: 'test_sub_ref')
          test_charge = create(:subscription_charge,
                              subscription: test_subscription,
                              user: test_subscription.user,
                              charge_amount: 50,
                              amount: 50,
                              pg_reference_id: 'test_payment_id')

          # Mock the signature verification
          allow_any_instance_of(CashfreeController).to receive(:verify_signature).and_return(true)

          # Mock the handle_initial_payment_refund method to create a refund
          allow_any_instance_of(SubscriptionCharge).to receive(:handle_initial_payment_refund) do |charge|
            # Create a refund record to simulate the method's behavior
            refund = SubscriptionChargeRefund.create!(
              subscription_charge: charge,
              user: charge.user,
              amount: 50.0,
              status: :initiated,
              reason: :initial_charge
            )
            # Mark the refund as success directly
            refund.update(status: :success)
            # Call the after_success callback manually
            charge.process_refund_completion(refund)
          end

          expect {
            post :subscription_callback, params: {
              cf_event: 'SUBSCRIPTION_REFUND_STATUS',
              cf_subReferenceId: test_subscription.pg_reference_id,
              cf_payment_id: test_charge.pg_reference_id,
              cf_merchant_refund_id: 'some-non-refund-id',
              cf_refund_amount: '50.0',
              cf_refund_status: 'SUCCESS',
              signature: 'test_signature'
            }
          }.to change(SubscriptionChargeRefund, :count).by(1)

          refund = SubscriptionChargeRefund.last
          expect(refund.amount).to eq(50.0)
          expect(refund.status).to eq('success')
          expect(response).to have_http_status(:ok)
        end

        it 'does not create duplicate refund records for the same amount' do
          # Update the subscription charge to have a non-zero charge_amount and amount
          subscription_charge.update(charge_amount: 50, amount: 50)

          # Create an actual refund record first
          existing_refund = SubscriptionChargeRefund.create!(
            subscription_charge: subscription_charge,
            user: subscription_charge.user,
            amount: 50.0,
            status: :success,
            reason: :initial_charge
          )

          # Mock the subscription_charges.find_by to return our subscription charge
          allow(subscription.subscription_charges).to receive(:find_by).and_return(subscription_charge)

          # Record the count before the test
          refund_count_before = SubscriptionChargeRefund.count

          post :subscription_callback, params: {
            cf_event: 'SUBSCRIPTION_REFUND_STATUS',
            cf_subReferenceId: subscription.pg_reference_id,
            cf_payment_id: subscription_charge.pg_reference_id,
            cf_refund_amount: '50.0',
            cf_refund_status: 'SUCCESS'
          }

          # Verify no new refunds were created
          expect(SubscriptionChargeRefund.count).to eq(refund_count_before)
          expect(response).to have_http_status(:ok)
        end
      end

      context 'with legacy refund handling' do
        it 'marks the refund as complete if refund status is SUCCESS and merchant_refund_id is not present' do
          # Update the subscription charge to have a non-zero charge_amount and amount
          subscription_charge.update(charge_amount: 50, amount: 50)

          # Mock the subscription_charges.find_by to return our subscription charge
          allow(subscription.subscription_charges).to receive(:find_by).with(pg_reference_id: 'payment_123').and_return(subscription_charge)

          # Mock the handle_initial_payment_refund method
          allow(subscription_charge).to receive(:handle_initial_payment_refund)

          # We need to stub the controller to call handle_initial_payment_refund
          allow_any_instance_of(CashfreeController).to receive(:subscription_callback) do |controller|
            controller.instance_variable_set(:@subscription, subscription)
            controller.instance_variable_set(:@subscription_charge, subscription_charge)
            subscription_charge.handle_initial_payment_refund
            controller.render json: { success: true }, status: :ok
          end

          post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_REFUND_STATUS',
                                                cf_subReferenceId: subscription.pg_reference_id,
                                                cf_payment_id: 'payment_123', cf_refund_status: 'SUCCESS' }

          expect(subscription_charge).to have_received(:handle_initial_payment_refund)
          expect(response).to have_http_status(:ok)
        end

        it 'notifies Honeybadger if refund status is not SUCCESS' do
          allow(Honeybadger).to receive(:notify)
          allow(subscription).to receive(:subscription_charges).and_return([subscription_charge])

          # Update the subscription charge to have a non-zero charge_amount
          subscription_charge.update(charge_amount: 50, amount: 50)

          # Mock the handle_initial_payment_refund method to avoid creating a refund
          allow(subscription_charge).to receive(:handle_initial_payment_refund)

          # Create a mock refund with a non-existent ID
          refund_id = '999999'
          allow(SubscriptionChargeRefund).to receive(:find_by).with(id: refund_id).and_return(nil)

          # Use a merchant_refund_id that starts with 'refund-' to trigger the Honeybadger notification
          post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_REFUND_STATUS',
                                                cf_subReferenceId: subscription.pg_reference_id,
                                                cf_payment_id: 'payment_123',
                                                cf_refund_status: 'FAILED',
                                                cf_merchant_refund_id: "refund-#{refund_id}-pg_123" }

          # Now we should get a 'Refund not found' notification
          expect(Honeybadger).to have_received(:notify).with('Refund not found', context: { params: anything })
          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)['message']).to eq('Refund failed')
        end

        it 'when subscription charge is not found' do
          allow(Honeybadger).to receive(:notify)

          # Mock the find_by to return nil to simulate subscription charge not found
          allow(subscription.subscription_charges).to receive(:find_by).with(pg_reference_id: 'payment_456').and_return(nil)

          post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_REFUND_STATUS', signature: 'cf_signature',
                                                cf_subReferenceId: subscription.pg_reference_id,
                                                cf_payment_id: 'payment_456',
                                                cf_merchant_refund_id: 'some-non-refund-id',
                                                cf_refund_status: 'SUCCESS' }

          expect(Honeybadger).to have_received(:notify).with('Subscription charge not found', context: { params: anything })
          expect(response).to have_http_status(:bad_request)
          expect(JSON.parse(response.body)['message']).to eq('Subscription charge not found')
        end

        it 'notifies Honeybadger if pg_reference_id is blank' do
          allow(Honeybadger).to receive(:notify)

          post :subscription_callback, params: { cf_event: 'SUBSCRIPTION_REFUND_STATUS',
                                                cf_subReferenceId: subscription.pg_reference_id,
                                                cf_merchant_refund_id: 'some-non-refund-id',
                                                cf_payment_id: nil,
                                                cf_refund_status: 'SUCCESS' }

          expect(Honeybadger).to have_received(:notify).with('Invalid refund callback', context: { params: anything })
          expect(response).to have_http_status(:bad_request)
          expect(JSON.parse(response.body)['message']).to eq('Invalid refund callback')
        end
      end
    end
  end
end
