require 'rails_helper'

RSpec.describe UserPosterPhoto, type: :model do
  let(:user) { FactoryBot.create(:user) }
  let(:photo) { FactoryBot.create(:photo) }
  let(:admin_medium) do
    image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
    create(:admin_medium, blob_data: image_630x940)
  end

  describe 'associations' do
    it 'belongs to user' do
      association = UserPosterPhoto.reflect_on_association(:user)
      expect(association.macro).to eq(:belongs_to)
    end

    it 'belongs to photo polymorphically' do
      association = UserPosterPhoto.reflect_on_association(:photo)
      expect(association.macro).to eq(:belongs_to)
      expect(association.options[:polymorphic]).to be true
    end

    it 'has working associations' do
      user_poster_photo = FactoryBot.create(:user_poster_photo, user: user, photo: photo)
      expect(user_poster_photo.user).to be_present
      expect(user_poster_photo.photo).to be_present
    end
  end

  describe 'validations' do
    it 'is invalid without pose' do
      user_poster_photo = FactoryBot.build(:user_poster_photo, user: user, photo: photo)
      user_poster_photo.pose = nil
      expect(user_poster_photo).not_to be_valid
      expect(user_poster_photo.errors[:pose]).to include("can't be blank")
    end

    it 'is invalid without photo' do
      user_poster_photo = FactoryBot.build(:user_poster_photo, user: user)
      user_poster_photo.photo = nil
      expect(user_poster_photo).not_to be_valid
      expect(user_poster_photo.errors[:photo]).to include("can't be blank")
    end

    it 'validates uniqueness of user_id scoped to pose' do
      FactoryBot.create(:user_poster_photo, user: user, photo: photo, pose: 'standing')
      duplicate = FactoryBot.build(:user_poster_photo, user: user, photo: photo, pose: 'standing')
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:user_id]).to include('has already been taken')
    end

    it 'allows same user with different pose' do
      FactoryBot.create(:user_poster_photo, user: user, photo: photo, pose: 'standing')
      different_pose = FactoryBot.build(:user_poster_photo, user: user, photo: photo, pose: 'sitting')
      expect(different_pose).to be_valid
    end

    it 'allows same pose with different user' do
      FactoryBot.create(:user_poster_photo, user: user, photo: photo, pose: 'standing')
      different_user = FactoryBot.create(:user)
      same_pose = FactoryBot.build(:user_poster_photo, user: different_user, photo: photo, pose: 'standing')
      expect(same_pose).to be_valid
    end

    it 'is valid with all required attributes' do
      user_poster_photo = FactoryBot.build(:user_poster_photo, user: user, photo: photo)
      expect(user_poster_photo).to be_valid
    end
  end

  describe 'factory' do
    it 'has a valid factory' do
      expect(build(:user_poster_photo)).to be_valid
    end

    it 'has a valid factory with admin medium' do
      expect(build(:user_poster_photo_with_admin_medium)).to be_valid
    end
  end

  describe 'photo validation' do

    context 'when photo is a Photo' do
      it 'is valid' do
        user_poster_photo = build(:user_poster_photo, user: user, photo: photo)
        expect(user_poster_photo).to be_valid
      end
    end

    context 'when photo is an AdminMedium' do
      it 'is valid' do
        user_poster_photo = build(:user_poster_photo, user: user, photo: admin_medium)
        expect(user_poster_photo).to be_valid
      end
    end

    context 'when photo is neither Photo nor AdminMedium' do
      it 'is invalid' do
        user_poster_photo = build(:user_poster_photo, user: user, photo: user)
        expect(user_poster_photo).not_to be_valid
        expect(user_poster_photo.errors[:photo]).to include('must be either a Photo or AdminMedium')
      end
    end
  end

  describe 'polymorphic photo association' do

    it 'can associate with Photo' do
      user_poster_photo = create(:user_poster_photo, user: user, photo: photo)
      expect(user_poster_photo.photo).to eq(photo)
      expect(user_poster_photo.photo_type).to eq('Photo')
    end

    it 'can associate with AdminMedium' do
      user_poster_photo = create(:user_poster_photo, user: user, photo: admin_medium)
      expect(user_poster_photo.photo).to eq(admin_medium)
      expect(user_poster_photo.photo_type).to eq('AdminMedium')
    end
  end

  describe 'user association' do
    let(:user) { create(:user) }
    let(:photo) { create(:photo) }

    it 'belongs to user' do
      user_poster_photo = create(:user_poster_photo, user: user, photo: photo)
      expect(user_poster_photo.user).to eq(user)
    end

    it 'can access user_poster_photos through user' do
      user_poster_photo = create(:user_poster_photo, user: user, photo: photo)
      expect(user.user_poster_photos).to include(user_poster_photo)
    end
  end

  describe 'photo association from Photo model' do
    let(:user) { create(:user) }
    let(:photo) { create(:photo) }

    it 'can access user_poster_photos through photo' do
      user_poster_photo = create(:user_poster_photo, user: user, photo: photo)
      expect(photo.user_poster_photos).to include(user_poster_photo)
    end
  end

  describe 'photo association from AdminMedium model' do

    it 'can access user_poster_photos through admin_medium' do
      user_poster_photo = create(:user_poster_photo, user: user, photo: admin_medium)
      expect(admin_medium.user_poster_photos).to include(user_poster_photo)
    end
  end
end
